<template>
  <div class="header-examples">
    <h1>动态宽度头部组件使用示例</h1>
    
    <!-- 示例1: 基础 DynamicHeader -->
    <section class="example-section">
      <h2>1. 基础 DynamicHeader 组件</h2>
      <div class="container" style="width: 800px; margin: 0 auto; border: 1px solid #ccc;">
        <DynamicHeader 
          :header-class="'custom-basic-header'"
          @width-updated="onBasicWidthUpdated"
        >
          <div class="basic-header-content">
            <h3>基础头部</h3>
            <button @click="showMessage('基础头部按钮点击')">操作</button>
          </div>
        </DynamicHeader>
        <div class="content-area">
          <p>当前头部宽度: {{ basicHeaderWidth }}px</p>
          <p>这是内容区域，头部会根据这个容器的宽度自动调整</p>
        </div>
      </div>
    </section>
    
    <!-- 示例2: EvaluationHeader -->
    <section class="example-section">
      <h2>2. EvaluationHeader 评估头部组件</h2>
      <div class="container" style="width: 1000px; margin: 0 auto; border: 1px solid #ccc;">
        <EvaluationHeader 
          :back-text="'返回评估列表'"
          @back="handleEvaluationBack"
          @width-updated="onEvaluationWidthUpdated"
        >
          <template #right>
            <div class="evaluation-actions">
              <button @click="saveEvaluation">保存</button>
              <button @click="submitEvaluation" class="primary">提交</button>
            </div>
          </template>
        </EvaluationHeader>
        <div class="content-area">
          <p>当前头部宽度: {{ evaluationHeaderWidth }}px</p>
          <p>这是评估问卷内容区域</p>
        </div>
      </div>
    </section>
    
    <!-- 示例3: ExamHeader -->
    <section class="example-section">
      <h2>3. ExamHeader 考试头部组件</h2>
      <div class="container" style="width: 1200px; margin: 0 auto; border: 1px solid #ccc;">
        <ExamHeader 
          :exam-title="'JavaScript 基础测试'"
          :current-question="currentQuestion"
          :total-questions="totalQuestions"
          :remaining-time="remainingTime"
          :show-timer="true"
          :show-progress="true"
          @back="handleExamBack"
          @width-updated="onExamWidthUpdated"
        >
          <template #actions>
            <button @click="prevQuestion" :disabled="currentQuestion <= 1">上一题</button>
            <button @click="nextQuestion" :disabled="currentQuestion >= totalQuestions">下一题</button>
            <button @click="submitExam" class="primary">交卷</button>
          </template>
        </ExamHeader>
        <div class="content-area">
          <p>当前头部宽度: {{ examHeaderWidth }}px</p>
          <p>这是考试内容区域</p>
          <div class="exam-controls">
            <button @click="startTimer">开始计时</button>
            <button @click="stopTimer">停止计时</button>
            <button @click="resetTimer">重置计时</button>
          </div>
        </div>
      </div>
    </section>
    
    <!-- 示例4: 响应式测试 -->
    <section class="example-section">
      <h2>4. 响应式宽度测试</h2>
      <div class="responsive-container" :style="{ width: containerWidth + 'px' }">
        <EvaluationHeader 
          :back-text="'测试返回'"
          @back="handleTestBack"
          @width-updated="onTestWidthUpdated"
        >
          <template #right>
            <span>容器宽度: {{ containerWidth }}px</span>
          </template>
        </EvaluationHeader>
        <div class="content-area">
          <p>当前头部宽度: {{ testHeaderWidth }}px</p>
          <div class="width-controls">
            <label>调整容器宽度:</label>
            <input 
              type="range" 
              v-model="containerWidth" 
              min="400" 
              max="1400" 
              step="50"
            />
            <span>{{ containerWidth }}px</span>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import DynamicHeader from '../components/DynamicHeader.vue';
import EvaluationHeader from '../components/EvaluationHeader.vue';
import ExamHeader from '../components/ExamHeader.vue';

export default {
  name: 'HeaderUsageExample',
  components: {
    DynamicHeader,
    EvaluationHeader,
    ExamHeader
  },
  data() {
    return {
      // 宽度记录
      basicHeaderWidth: 0,
      evaluationHeaderWidth: 0,
      examHeaderWidth: 0,
      testHeaderWidth: 0,
      
      // 考试相关数据
      currentQuestion: 1,
      totalQuestions: 20,
      remainingTime: 3600, // 60分钟
      timer: null,
      
      // 响应式测试
      containerWidth: 800
    };
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer);
    }
  },
  methods: {
    // 宽度更新回调
    onBasicWidthUpdated(width) {
      this.basicHeaderWidth = width;
    },
    onEvaluationWidthUpdated(width) {
      this.evaluationHeaderWidth = width;
    },
    onExamWidthUpdated(width) {
      this.examHeaderWidth = width;
    },
    onTestWidthUpdated(width) {
      this.testHeaderWidth = width;
    },
    
    // 基础操作
    showMessage(message) {
      alert(message);
    },
    
    // 评估相关操作
    handleEvaluationBack() {
      console.log('返回评估列表');
    },
    saveEvaluation() {
      console.log('保存评估');
    },
    submitEvaluation() {
      console.log('提交评估');
    },
    
    // 考试相关操作
    handleExamBack() {
      if (confirm('确定要退出考试吗？')) {
        console.log('退出考试');
      }
    },
    prevQuestion() {
      if (this.currentQuestion > 1) {
        this.currentQuestion--;
      }
    },
    nextQuestion() {
      if (this.currentQuestion < this.totalQuestions) {
        this.currentQuestion++;
      }
    },
    submitExam() {
      if (confirm('确定要交卷吗？')) {
        console.log('提交考试');
      }
    },
    
    // 计时器操作
    startTimer() {
      if (!this.timer) {
        this.timer = setInterval(() => {
          if (this.remainingTime > 0) {
            this.remainingTime--;
          } else {
            this.stopTimer();
            alert('考试时间到！');
          }
        }, 1000);
      }
    },
    stopTimer() {
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
      }
    },
    resetTimer() {
      this.stopTimer();
      this.remainingTime = 3600;
    },
    
    // 测试操作
    handleTestBack() {
      console.log('测试返回');
    }
  }
};
</script>

<style lang="scss" scoped>
.header-examples {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.example-section {
  margin-bottom: 40px;
  
  h2 {
    color: #262626;
    margin-bottom: 16px;
  }
}

.container {
  position: relative;
  border: 2px solid #e9e9e9;
  border-radius: 8px;
  overflow: hidden;
}

.responsive-container {
  position: relative;
  border: 2px solid #1890ff;
  border-radius: 8px;
  overflow: hidden;
  margin: 0 auto;
  transition: width 0.3s ease;
}

.content-area {
  padding: 60px 20px 20px;
  min-height: 200px;
  background: #fafafa;
}

.basic-header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0 20px;
  
  h3 {
    margin: 0;
    color: #262626;
  }
  
  button {
    padding: 6px 12px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    background: #fff;
    cursor: pointer;
    
    &:hover {
      border-color: #40a9ff;
      color: #40a9ff;
    }
  }
}

.evaluation-actions,
.exam-controls,
.width-controls {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 16px;
  
  button {
    padding: 6px 12px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    background: #fff;
    cursor: pointer;
    
    &:hover {
      border-color: #40a9ff;
      color: #40a9ff;
    }
    
    &.primary {
      background: #1890ff;
      border-color: #1890ff;
      color: #fff;
      
      &:hover {
        background: #40a9ff;
      }
    }
    
    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }
  
  input[type="range"] {
    width: 200px;
    margin: 0 8px;
  }
  
  label {
    font-weight: 500;
  }
}

.custom-basic-header {
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  
  h3 {
    color: #fff;
  }
  
  button {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
    color: #fff;
    
    &:hover {
      background: rgba(255, 255, 255, 0.3);
      border-color: rgba(255, 255, 255, 0.5);
    }
  }
}
</style>
