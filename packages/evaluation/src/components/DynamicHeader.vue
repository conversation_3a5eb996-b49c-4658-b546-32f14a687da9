<template>
  <div ref="dynamicHeader" class="dynamic-header" :class="headerClass">
    <slot></slot>
  </div>
</template>

<script>
export default {
  name: 'DynamicHeader',
  props: {
    // 自定义样式类名
    headerClass: {
      type: [String, Array, Object],
      default: ''
    },
    // 是否启用动态宽度调整
    enableDynamicWidth: {
      type: Boolean,
      default: true
    },
    // 宽度偏移量（可以用于微调）
    widthOffset: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      resizeObserver: null
    };
  },
  mounted() {
    if (this.enableDynamicWidth) {
      this.$nextTick(() => {
        this.setHeaderWidth();
        this.initResizeObserver();
      });
    }
  },
  beforeDestroy() {
    this.cleanup();
  },
  methods: {
    // 设置header宽度以匹配父元素
    setHeaderWidth() {
      const header = this.$refs.dynamicHeader;
      if (header && this.enableDynamicWidth) {
        const parentElement = this.$parent.$el || this.$el.parentElement;
        if (parentElement) {
          const parentWidth = parentElement.offsetWidth + this.widthOffset;
          header.style.width = `${parentWidth}px`;
          
          // 触发自定义事件，通知父组件宽度已更新
          this.$emit('width-updated', parentWidth);
        }
      }
    },
    
    // 初始化ResizeObserver来监听父元素尺寸变化
    initResizeObserver() {
      if (typeof ResizeObserver !== 'undefined') {
        const parentElement = this.$parent.$el || this.$el.parentElement;
        if (parentElement) {
          this.resizeObserver = new ResizeObserver(() => {
            this.setHeaderWidth();
          });
          this.resizeObserver.observe(parentElement);
        }
      }
      
      // 兜底方案：监听窗口大小变化
      window.addEventListener('resize', this.handleResize);
    },
    
    // 处理窗口大小变化
    handleResize() {
      this.setHeaderWidth();
    },
    
    // 清理资源
    cleanup() {
      if (this.resizeObserver) {
        this.resizeObserver.disconnect();
        this.resizeObserver = null;
      }
      window.removeEventListener('resize', this.handleResize);
    },
    
    // 手动触发宽度更新（供外部调用）
    updateWidth() {
      this.setHeaderWidth();
    }
  }
};
</script>

<style lang="scss" scoped>
.dynamic-header {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  box-sizing: border-box;
  width: 100%;
  
  /* 动态宽度设置 - 继承父元素宽度 */
  max-width: inherit;
}
</style>
