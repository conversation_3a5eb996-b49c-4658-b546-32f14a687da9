<template>
  <DynamicHeader 
    :header-class="['exam-header', customClass]"
    :enable-dynamic-width="enableDynamicWidth"
    :width-offset="widthOffset"
    @width-updated="onWidthUpdated"
  >
    <div class="exam-header-content">
      <!-- 左侧：返回按钮 -->
      <div class="exam-header-left">
        <div class="flex flex-mid hand hover-primary-6" @click="handleBack">
          <yxt-svg icon-class="quit" width="16px" height="16px" />
          <span class="ml8">{{ backText || '退出考试' }}</span>
        </div>
      </div>
      
      <!-- 中间：考试信息 -->
      <div class="exam-header-center">
        <div v-if="examTitle" class="exam-title">{{ examTitle }}</div>
        <div v-if="showProgress" class="exam-progress">
          第 {{ currentQuestion }} / {{ totalQuestions }} 题
        </div>
      </div>
      
      <!-- 右侧：时间和操作 -->
      <div class="exam-header-right">
        <div v-if="showTimer && remainingTime > 0" class="exam-timer">
          <yxt-svg icon-class="time" width="16px" height="16px" />
          <span class="ml4">{{ formatTime(remainingTime) }}</span>
        </div>
        
        <div v-if="$slots.actions" class="exam-actions ml16">
          <slot name="actions"></slot>
        </div>
      </div>
    </div>
  </DynamicHeader>
</template>

<script>
import DynamicHeader from './DynamicHeader.vue';

export default {
  name: 'ExamHeader',
  components: {
    DynamicHeader
  },
  props: {
    // 返回按钮文本
    backText: {
      type: String,
      default: '退出考试'
    },
    // 考试标题
    examTitle: {
      type: String,
      default: ''
    },
    // 当前题目序号
    currentQuestion: {
      type: Number,
      default: 1
    },
    // 总题目数
    totalQuestions: {
      type: Number,
      default: 0
    },
    // 是否显示进度
    showProgress: {
      type: Boolean,
      default: true
    },
    // 剩余时间（秒）
    remainingTime: {
      type: Number,
      default: 0
    },
    // 是否显示计时器
    showTimer: {
      type: Boolean,
      default: true
    },
    // 自定义样式类名
    customClass: {
      type: [String, Array, Object],
      default: ''
    },
    // 是否启用动态宽度调整
    enableDynamicWidth: {
      type: Boolean,
      default: true
    },
    // 宽度偏移量
    widthOffset: {
      type: Number,
      default: 0
    }
  },
  methods: {
    handleBack() {
      this.$emit('back');
    },
    
    onWidthUpdated(width) {
      this.$emit('width-updated', width);
    },
    
    // 格式化时间显示
    formatTime(seconds) {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      const secs = seconds % 60;
      
      if (hours > 0) {
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
      } else {
        return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.exam-header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 44px;
  padding: 0 24px 0 16px;
  color: #595959;
  background: #fff;
  box-shadow: inset 0 -1px 0 0 #e9e9e9;
}

.exam-header-left {
  flex-shrink: 0;
}

.exam-header-center {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  
  .exam-title {
    font-size: 16px;
    font-weight: 500;
    color: #262626;
    line-height: 1.2;
  }
  
  .exam-progress {
    font-size: 12px;
    color: #8c8c8c;
    margin-top: 2px;
  }
}

.exam-header-right {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  
  .exam-timer {
    display: flex;
    align-items: center;
    padding: 4px 8px;
    background: #f5f5f5;
    border-radius: 4px;
    font-size: 14px;
    color: #595959;
    
    &.warning {
      background: #fff7e6;
      color: #fa8c16;
    }
    
    &.danger {
      background: #fff2f0;
      color: #ff4d4f;
    }
  }
}

.exam-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}
</style>
