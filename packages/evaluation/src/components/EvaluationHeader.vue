<template>
  <div ref="dynamicHeader" class="dynamic-header" :class="['evaluation-questionnair-header', customClass]">
    <div class="flex flex-mid hand hover-primary-6" @click="handleBack">
      <yxt-svg icon-class="quit" width="16px" height="16px" />
      <span class="ml8">{{ backText || $t("pc_ote_btn_exit" /** 退出 */) }}</span>
    </div>

    <!-- 右侧插槽，可以放置其他内容 -->
    <div v-if="$slots.right" class="evaluation-header-right">
      <slot name="right"></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'EvaluationHeader',
  props: {
    // 返回按钮文本
    backText: {
      type: String,
      default: ''
    },
    // 自定义样式类名
    customClass: {
      type: [String, Array, Object],
      default: ''
    },
    // 宽度偏移量
    widthOffset: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      resizeObserver: null
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.setHeaderWidth();
      this.initResizeObserver();
    });
  },
  beforeDestroy() {
    this.cleanup();
  },
  methods: {
    handleBack() {
      this.$emit('back');
    },

    // 设置header宽度以匹配父元素
    setHeaderWidth() {
      const header = this.$refs.dynamicHeader;
      if (header) {
        const parentElement = this.$parent.$el || this.$el.parentElement;
        if (parentElement) {
          const parentWidth = parentElement.offsetWidth + this.widthOffset;
          header.style.width = `${parentWidth}px`;

          // 触发自定义事件，通知父组件宽度已更新
          this.$emit('width-updated', parentWidth);
        }
      }
    },

    // 初始化ResizeObserver来监听父元素尺寸变化
    initResizeObserver() {
      if (typeof ResizeObserver !== 'undefined') {
        const parentElement = this.$parent.$el || this.$el.parentElement;
        if (parentElement) {
          this.resizeObserver = new ResizeObserver(() => {
            this.setHeaderWidth();
          });
          this.resizeObserver.observe(parentElement);
        }
      }

      // 兜底方案：监听窗口大小变化
      window.addEventListener('resize', this.handleResize);
    },

    // 处理窗口大小变化
    handleResize() {
      this.setHeaderWidth();
    },

    // 清理资源
    cleanup() {
      if (this.resizeObserver) {
        this.resizeObserver.disconnect();
        this.resizeObserver = null;
      }
      window.removeEventListener('resize', this.handleResize);
    }
  }
};
</script>

<style lang="scss" scoped>
.dynamic-header {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  box-sizing: border-box;
  width: 100%;

  /* 动态宽度设置 - 继承父元素宽度 */
  max-width: inherit;
}

.evaluation-header-right {
  margin-left: auto;
}
</style>
