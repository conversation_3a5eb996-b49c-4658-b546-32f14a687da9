<template>
  <DynamicHeader 
    :header-class="['evaluation-questionnair-header', customClass]"
    :enable-dynamic-width="enableDynamicWidth"
    :width-offset="widthOffset"
    @width-updated="onWidthUpdated"
  >
    <div class="flex flex-mid hand hover-primary-6" @click="handleBack">
      <yxt-svg icon-class="quit" width="16px" height="16px" />
      <span class="ml8">{{ backText || $t("pc_ote_btn_exit" /** 退出 */) }}</span>
    </div>
    
    <!-- 右侧插槽，可以放置其他内容 -->
    <div v-if="$slots.right" class="evaluation-header-right">
      <slot name="right"></slot>
    </div>
  </DynamicHeader>
</template>

<script>
import DynamicHeader from './DynamicHeader.vue';

export default {
  name: 'EvaluationHeader',
  components: {
    DynamicHeader
  },
  props: {
    // 返回按钮文本
    backText: {
      type: String,
      default: ''
    },
    // 自定义样式类名
    customClass: {
      type: [String, Array, Object],
      default: ''
    },
    // 是否启用动态宽度调整
    enableDynamicWidth: {
      type: Boolean,
      default: true
    },
    // 宽度偏移量
    widthOffset: {
      type: Number,
      default: 0
    }
  },
  methods: {
    handleBack() {
      this.$emit('back');
    },
    
    onWidthUpdated(width) {
      this.$emit('width-updated', width);
    }
  }
};
</script>

<style lang="scss" scoped>
.evaluation-header-right {
  margin-left: auto;
}
</style>
