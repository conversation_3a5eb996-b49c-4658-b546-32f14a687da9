# 动态宽度组件使用说明

## 组件介绍

### DynamicHeader 基础组件
一个通用的动态宽度头部组件，可以根据父元素宽度自动调整自身宽度。

### EvaluationHeader 评估头部组件
基于 DynamicHeader 的评估问卷专用头部组件，包含退出按钮和自定义内容插槽。

## 使用方法

### 1. 使用 DynamicHeader 基础组件

```vue
<template>
  <div class="container">
    <DynamicHeader 
      :header-class="'my-custom-header'"
      :enable-dynamic-width="true"
      :width-offset="0"
      @width-updated="onWidthUpdated"
    >
      <!-- 自定义头部内容 -->
      <div class="header-content">
        <h1>我的标题</h1>
        <button @click="handleAction">操作按钮</button>
      </div>
    </DynamicHeader>
  </div>
</template>

<script>
import DynamicHeader from './components/DynamicHeader.vue';

export default {
  components: {
    DynamicHeader
  },
  methods: {
    onWidthUpdated(width) {
      console.log('Header width updated:', width);
    },
    handleAction() {
      // 处理按钮点击
    }
  }
};
</script>
```

### 2. 使用 EvaluationHeader 评估头部组件

```vue
<template>
  <div class="evaluation-container">
    <EvaluationHeader 
      :back-text="'返回列表'"
      :custom-class="'my-evaluation-header'"
      :enable-dynamic-width="true"
      @back="handleBack"
      @width-updated="onWidthUpdated"
    >
      <!-- 右侧自定义内容 -->
      <template #right>
        <div class="header-actions">
          <button @click="save">保存</button>
          <button @click="submit">提交</button>
        </div>
      </template>
    </EvaluationHeader>
    
    <!-- 页面内容 -->
    <div class="content">
      <!-- 内容区域 -->
    </div>
  </div>
</template>

<script>
import EvaluationHeader from './components/EvaluationHeader.vue';

export default {
  components: {
    EvaluationHeader
  },
  methods: {
    handleBack() {
      // 处理返回操作
      this.$router.go(-1);
    },
    onWidthUpdated(width) {
      console.log('Header width updated:', width);
    },
    save() {
      // 保存操作
    },
    submit() {
      // 提交操作
    }
  }
};
</script>
```

### 3. 在其他评估相关页面中使用

```vue
<template>
  <div class="exam-container">
    <EvaluationHeader 
      :back-text="'退出考试'"
      @back="confirmExit"
    >
      <template #right>
        <div class="exam-info">
          <span>剩余时间: {{ remainingTime }}</span>
        </div>
      </template>
    </EvaluationHeader>
    
    <!-- 考试内容 -->
  </div>
</template>
```

## 属性说明

### DynamicHeader Props

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| headerClass | String/Array/Object | '' | 自定义样式类名 |
| enableDynamicWidth | Boolean | true | 是否启用动态宽度调整 |
| widthOffset | Number | 0 | 宽度偏移量（像素） |

### DynamicHeader Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| width-updated | width: Number | 宽度更新时触发 |

### EvaluationHeader Props

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| backText | String | '' | 返回按钮文本 |
| customClass | String/Array/Object | '' | 自定义样式类名 |
| enableDynamicWidth | Boolean | true | 是否启用动态宽度调整 |
| widthOffset | Number | 0 | 宽度偏移量 |

### EvaluationHeader Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| back | - | 点击返回按钮时触发 |
| width-updated | width: Number | 宽度更新时触发 |

### EvaluationHeader Slots

| 插槽名 | 说明 |
|--------|------|
| right | 右侧内容插槽 |

## 样式自定义

组件提供了基础样式，你可以通过传入自定义类名来覆盖样式：

```scss
.my-custom-header {
  background-color: #f0f0f0;
  border-bottom: 2px solid #ccc;
  
  .header-content {
    padding: 10px 20px;
  }
}
```

## 注意事项

1. 组件会自动监听父元素的尺寸变化，无需手动调用更新方法
2. 支持现代浏览器的 ResizeObserver API，对于不支持的浏览器会降级到监听 window resize 事件
3. 组件销毁时会自动清理事件监听器和 ResizeObserver
4. 如果需要手动触发宽度更新，可以调用组件的 `updateWidth()` 方法
