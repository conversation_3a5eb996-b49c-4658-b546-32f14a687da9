# EvaluationHeader 动态宽度头部组件

一个可以根据父元素宽度自动调整自身宽度的头部组件。

## 使用方法

```vue
<template>
  <div class="container">
    <EvaluationHeader 
      :back-text="'返回列表'"
      @back="handleBack"
      @width-updated="onWidthUpdated"
    >
      <!-- 右侧自定义内容 -->
      <template #right>
        <button>保存</button>
        <button>提交</button>
      </template>
    </EvaluationHeader>
    
    <!-- 页面内容 -->
    <div class="content">
      <!-- 内容区域 -->
    </div>
  </div>
</template>

<script>
import EvaluationHeader from './components/EvaluationHeader.vue';

export default {
  components: {
    EvaluationHeader
  },
  methods: {
    handleBack() {
      // 处理返回操作
      this.$emit('back', 'Questionnaire');
    },
    onWidthUpdated(width) {
      console.log('Header width updated:', width);
    }
  }
};
</script>
```

## 属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| backText | String | '' | 返回按钮文本，为空时使用默认的国际化文本 |
| customClass | String/Array/Object | '' | 自定义样式类名 |
| widthOffset | Number | 0 | 宽度偏移量（像素） |

## 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| back | - | 点击返回按钮时触发 |
| width-updated | width: Number | 宽度更新时触发 |

## 插槽

| 插槽名 | 说明 |
|--------|------|
| right | 右侧内容插槽 |

## 特性

- ✅ 自动根据父元素宽度调整自身宽度
- ✅ 支持现代浏览器的 ResizeObserver API
- ✅ 兼容性降级到 window resize 事件
- ✅ 组件销毁时自动清理事件监听器
- ✅ 支持自定义样式和内容
