<template>
  <div
    v-if="!byClass"
    class="yxtulcdsdk-sticky"
    :style="{
      height:height + 'px',
      flexShrink: 0
    }"
  >
    <div :style="style">
      <slot></slot>
    </div>
  </div>
  <div v-else :style="stickyClassStyle">
    <slot></slot>
  </div>
</template>

<script>
import { getScrollParent } from '../../core/utils';
export default {
  name: 'CommonSticky',
  props: {
    position: {
      type: String,
      default: 'bottom' // top , bottom
    },
    offset: {
      type: Number,
      default: 0
    },
    forceFixed: {
      type: Boolean,
      default: false
    },
    byClass: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      scroller: null,
      style: {},
      height: 0,
      stickyClassStyle: ''
    };
  },
  mounted() {
    debugger
    if (this.byClass) {
      const style = {};
      style.position = 'sticky';
      if (this.position === 'top') {
        style.top = this.offset + 'px';
      } else if (this.position === 'bottom') {
        style.bottom = this.offset + 'px';
      }
      this.stickyClassStyle = style;
    } else {
      this.requestAnimation = null;
      // 寻找可滚动的父元素
      this.scroller = getScrollParent(this.$el);
      this.scroller.addEventListener('scroll', this.scrollHandler);
      window.addEventListener('resize', this.scrollHandler);
      this.resetHeight();

      // 处理可能外层的scrollbar 的 display:none、内部元素变动等 导致高度获取错误的问题。这里尝试多获取纠正
      let count = 0;
      const itv = setInterval(()=>{
        this.resetHeight();
        count++;
        if (this.height || count > 10) {
          clearInterval(itv);
        }
      }, 300);

      this.scrollHandler();
    }

    setTimeout(() => {
      this.resetHeight();
    }, 300);
  },
  beforeDestroy() {
    this.scroller && this.scroller.removeEventListener('scroll', this.scrollHandler);
    window.removeEventListener('resize', this.scrollHandler);
  },
  methods: {
    resetHeight() {
      this.height = this.$slots.default[0].elm.clientHeight; // 插槽的高度
    },
    scrollHandler() {
      if (this.requestAnimation) {
        window.cancelAnimationFrame(this.requestAnimation);
      }
      this.requestAnimation = window.requestAnimationFrame(() => {
        this.resetFun();
      });
    },
    resetFun() {
      if (!this.scroller) {
        return;
      }
      let scrollerRect = {};
      if ([window, document, document.documentElement, null, undefined].includes(this.scroller)) {
        scrollerRect = {
          top: 0,
          right: window.innerWidth,
          bottom: window.innerHeight,
          left: 0
        };
      } else {
        scrollerRect = this.scroller.getBoundingClientRect();
      }
      const elRect = this.$el.getBoundingClientRect();
      const diff = this.position === 'bottom' && this.scroller.clientWidth < this.scroller.scrollWidth ? 15 : 0;
      const isFixed = this.forceFixed || (this.position === 'bottom' && scrollerRect.bottom - elRect.bottom <= (this.offset + diff)) || (this.position === 'top' && scrollerRect.top - elRect.top + this.offset >= 0);
      this.changeStyle(isFixed, elRect.left, elRect.width, this.offset + diff);
    },
    changeStyle(fixed, left, width, offset) {
      const style = {};
      if (fixed) {
        style.position = 'fixed';
        style.left = left + 'px';
        style.width = width + 'px';
        if (this.position === 'top') {
          style.top = offset + 'px';
        } else if (this.position === 'bottom') {
          style.bottom = offset + 'px';
        }
        style.zIndex = 3;
      }
      this.style = style;

    }
  }
};
</script>
