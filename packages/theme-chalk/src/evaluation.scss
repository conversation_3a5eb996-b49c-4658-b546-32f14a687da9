@import "mixins/mixins";
@import "common/var";

@include b(evaluation) {
  overflow: hidden;
  background: #f5f5f5;

  &--radius {
    border-radius: 12px;

    .survey__description-container {
      overflow: hidden;
      border-radius: 12px;
    }
  }

  &-questionnaire {
    .yxt-scrollbar__view {
      position: relative;
      display: flex;
      flex-direction: column;
      min-height: 100%;
    }
  }
  .evaluation-questionnair-header {
    position: fixed;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
    width: 100%;
    height: 44px;
    padding: 0 24px 0 16px;
    color: #595959;
    background: #fff;
    box-shadow: inset 0 -1px 0 0 #e9e9e9;
    z-index: 999;

    /* 动态宽度设置 - 继承父元素宽度 */
    max-width: inherit;
  }

  &-wrap {
    box-sizing: border-box;
    min-width: 850px;
    min-height: 100%;
    margin: 0 auto;
  }

  .flex-row-between {
    display: flex;
    justify-content: space-between;
  }

  .flex-center,
  .flex-row-center,
  .flex-align-center {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .gwnl-flex,
  .layout-flex {
    display: flex;
  }

  .justify-content-between {
    justify-content: space-between;
  }

  .pos-relative {
    position: relative;
  }

  .pos-absolute {
    position: absolute;
  }

  .w280 {
    width: 280px;
  }

  .w282 {
    width: 282px;
  }

  .w292 {
    width: 292px;
  }

  .w570 {
    width: 570px;
  }

  .pwidth50 {
    width: 50%;
  }

  .pwidth-100 {
    width: 100px;
  }

  .minW82 {
    min-width: 82px;
  }

  .minW100 {
    min-width: 100px;
  }

  .gwnl-eval__container,
  .pwidth100,
  .width100 {
    width: 100%;
  }

  .bg-color-d7f7c1 {
    background-color: #d7f7c1;
  }

  .bg-color-fef2f0 {
    background-color: #fef2f0;
  }

  .bg-color-c7d9ff {
    background-color: #c7d9ff;
  }

  .bg-light {
    background-color: rgb(251 251 253);
  }

  .color-red-fa {
    color: #f5222d;
  }

  .top4 {
    top: 4px;
  }

  .visibility-show {
    visibility: visible;
  }

  .visibility-hide {
    visibility: hidden;
  }
}
